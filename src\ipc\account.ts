import fs from 'fs';
import readline from 'readline';

import { dialog, type IpcMain, type IpcMainInvokeEvent, type BrowserWindow } from 'electron';

import { AccountImportService, type EmailServerConfig } from '../services/accountImportService';
import { discoverEmailConfig, type DiscoveredConfig } from '../services/autoDiscoveryService';
import { imapFlowConnectionManager } from '../services/imapFlowConnectionManager';
import { InstantImportService } from '../services/instantImportService';
import {
    getAccounts,
    addAccount,
    updateAccount,
    removeAccount,
    addAccounts,
    getDomains,
    saveDomain,
    removeDomain
} from '../services/storeService';
import type { Account } from '../shared/types/account';

type SendLogFn = (level: 'info' | 'success' | 'error', message: string) => void;

/**
 * Convert DiscoveredConfig to EmailServerConfig format
 */
function convertToEmailServerConfig(config: DiscoveredConfig | null): EmailServerConfig | null {
    if (!config) return null;

    return {
        imap: config.imap ? {
            host: config.imap.host,
            port: config.imap.port,
            secure: config.imap.secure
        } : undefined,
        smtp: config.smtp ? {
            host: config.smtp.host,
            port: config.smtp.port,
            secure: config.smtp.secure
        } : undefined
    };
}

/**
 * A wrapper for discoverEmailConfig that caches results in domains.txt.
 */
async function getEmailConfig(domain: string, sendLog: SendLogFn, force: boolean = false): Promise<DiscoveredConfig | null> {
    sendLog('info', `getEmailConfig called for ${domain} with force=${force}`);
    const savedDomains = await getDomains();

    // 1. Check our saved domains first (skip if force is true)
    if (!force && domain in savedDomains && savedDomains[domain] !== null && savedDomains[domain] !== undefined) {
        sendLog('info', `Found saved config for ${domain}`);
        // No need to parse JSON anymore, getDomains returns a parsed object
        return savedDomains[domain];
    }

    // 2. If not found or force is true, run auto-discovery
    if (force) {
        sendLog('info', `Force discovery requested for ${domain}. Running auto-discovery...`);
    } else {
        sendLog('info', `No saved config for ${domain}. Running auto-discovery...`);
    }
    const config = await discoverEmailConfig(domain, sendLog as any, { force });

    // 3. Save the result for next time
    if (config) {
        sendLog('success', `Successfully discovered config for ${domain}. Saving...`);
        // Pass the raw config object to saveDomain, casting it to ensure compatibility
        await saveDomain(domain, config);
    }

    return config;
}

/**
 * Register discovery and domain handlers
 */
function registerDiscoveryHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    ipcMain.handle('discover:email-config', async (_event: IpcMainInvokeEvent, domain: string, force: boolean = false) => {
        // This handler now uses our caching wrapper
        sendLog('info', `IPC discover:email-config called for ${domain} with force=${force}`);
        return getEmailConfig(domain, sendLog, force);
    });

    ipcMain.handle('domains:get', async () => {
        return await getDomains();
    });

    ipcMain.handle('domains:save', async (_event: IpcMainInvokeEvent, domain: string, config: DiscoveredConfig) => {
        await saveDomain(domain, config);
        sendLog('info', `Saved domain configuration for: ${domain}`);
        return { success: true };
    });

    ipcMain.handle('domains:remove', async (_event: IpcMainInvokeEvent, domain: string) => {
        await removeDomain(domain);
        sendLog('info', `Removed domain configuration for: ${domain}`);
        return { success: true };
    });
}

/**
 * Register basic account CRUD handlers
 */
function registerAccountCrudHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    ipcMain.handle('accounts:get', async () => {
        return await getAccounts();
    });

    ipcMain.handle('accounts:get-by-id', async (_event: IpcMainInvokeEvent, accountId: string) => {
        const accounts = await getAccounts();
        return accounts.find(acc => acc.id === accountId) || null;
    });

    ipcMain.handle('accounts:add', async (_event: IpcMainInvokeEvent, accountData: Omit<Account, 'id'>) => {
        console.log('IPC accounts:add called with:', accountData);

        // Save custom IMAP settings to domains.txt for future use
        const domain = accountData.email.split('@')[1];
        if ((domain?.length ?? 0) > 0 && accountData.incoming !== null && accountData.incoming !== undefined) {
            // Don't save example domains or configurations with example hosts
            if (domain.includes('example.com') || domain.includes('example.org') ||
                accountData.incoming.host.includes('example.com') || accountData.incoming.host.includes('example.org')) {
                console.log('Skipping domain save for example domain/host:', domain, accountData.incoming.host);
            } else {
                const savedDomains = await getDomains();

                // Check if this domain doesn't exist in our saved domains or has different settings
                const existingConfig = savedDomains[domain];
                const currentConfig: DiscoveredConfig = {};

            // Build current config from account data
            if (accountData.incoming !== null && accountData.incoming !== undefined) {
                currentConfig.imap = {
                    host: accountData.incoming.host,
                    port: accountData.incoming.port,
                    secure: accountData.incoming.useTls
                };
            }

            if (accountData.outgoing) {
                currentConfig.smtp = {
                    host: accountData.outgoing.host,
                    port: accountData.outgoing.port,
                    secure: accountData.outgoing.useTls
                };
            }

            // Save if domain doesn't exist or settings are different
            const shouldSave = existingConfig === null || existingConfig === undefined ||
                (currentConfig.imap !== null && currentConfig.imap !== undefined &&
                 (existingConfig.imap === null || existingConfig.imap === undefined ||
                    existingConfig.imap.host !== currentConfig.imap.host ||
                    existingConfig.imap.port !== currentConfig.imap.port ||
                    existingConfig.imap.secure !== currentConfig.imap.secure));

                if (shouldSave === true) {
                    sendLog('info', `Saving custom IMAP settings for domain: ${domain}`);
                    await saveDomain(domain, currentConfig);
                }
            }
        }

        sendLog('info', `Adding account: ${accountData.email}`);
        console.log('Calling addAccount with:', accountData);
        const result = await addAccount(accountData);
        console.log('addAccount returned:', result);
        return result;
    });

    ipcMain.handle('accounts:update', async (_event: IpcMainInvokeEvent, accountId: string, accountData: Partial<Omit<Account, 'id'>>) => {
        console.log('IPC accounts:update called with accountId:', accountId);
        console.log('IPC accounts:update accountData:', JSON.stringify(accountData, null, 2));

        const updatedAccount = await updateAccount(accountId, accountData);
        if (!updatedAccount) {
            throw new Error('Failed to find account to update.');
        }

        console.log('IPC accounts:update returning:', JSON.stringify(updatedAccount, null, 2));
        return updatedAccount;
    });

    ipcMain.handle('accounts:delete', async (_event: IpcMainInvokeEvent, accountId: string) => {
        try {
            await removeAccount(accountId);
            void imapFlowConnectionManager.end(accountId);
            return { success: true };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Failed to delete account ${accountId}:`, error);
            throw new Error('Failed to delete account from store.');
        }
    });
}

/**
 * Register file import handlers
 */
function registerFileImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    ipcMain.handle('accounts:import-from-file', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Import Accounts',
            buttonLabel: 'Import',
            properties: ['openFile'],
            filters: [{ name: 'Text Files', extensions: ['txt', 'csv'] }],
        });

        if (canceled || filePaths.length === 0) {
            return { addedCount: 0, skippedCount: 0, error: 'File selection canceled.' };
        }

        const filePath = filePaths[0];

        try {
            sendLog('info', 'Starting account import...');

            // Use AccountImportService for parsing with Microsoft token support
            const parseResult = await AccountImportService.parseFile(filePath);

            if (!parseResult.success) {
                sendLog('error', `Parse failed: ${parseResult.errors.join(', ')}`);
                return { addedCount: 0, skippedCount: parseResult.skippedLines, error: parseResult.errors.join(', ') };
            }

            sendLog('info', `Parsed ${parseResult.accounts.length} accounts. Configuring servers...`);

            // Configure accounts with server discovery
            const configuredAccounts = await AccountImportService.configureAccounts(
                parseResult.accounts,
                async (email) => {
                    const config = await getEmailConfig(email, sendLog);
                    return convertToEmailServerConfig(config);
                }
            );

            // Add to store
            const newAccounts = await addAccounts(configuredAccounts);

            sendLog('success', `Successfully imported ${newAccounts.length} accounts`);

            return {
                addedCount: newAccounts.length,
                skippedCount: parseResult.skippedLines,
                totalCount: parseResult.totalLines,
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Import failed';
            sendLog('error', `Import failed: ${errorMessage}`);
            return { addedCount: 0, skippedCount: 0, error: errorMessage };
        }
    });
}

/**
 * Register preview and instant import handlers
 */
function registerPreviewImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    // Enhanced import with preview and progress
    ipcMain.handle('accounts:import-preview', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Preview Import File',
            buttonLabel: 'Preview',
            properties: ['openFile'],
            filters: [
                { name: 'Text Files', extensions: ['txt', 'csv'] },
                { name: 'All Files', extensions: ['*'] }
            ],
        });

        if (canceled || filePaths.length === 0) {
            return { success: false, error: 'File selection canceled.' };
        }

        try {
            const preview = await AccountImportService.generatePreview(filePaths[0]);
            return { success: true, preview, filePath: filePaths[0] };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to preview file';
            sendLog('error', `Preview failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });

    ipcMain.handle('accounts:import-enhanced', async (event: IpcMainInvokeEvent, filePath: string) => {
        try {
            sendLog('info', 'Starting enhanced import...');

            // Parse the file with progress reporting
            const parseResult = await AccountImportService.parseFile(filePath, (progress) => {
                // Send progress updates to renderer
                event.sender.send('import:progress', progress);
            });

            if (!parseResult.success) {
                sendLog('error', `Parse failed: ${parseResult.errors.join(', ')}`);
                return { success: false, errors: parseResult.errors };
            }

            sendLog('info', `Parsed ${parseResult.accounts.length} accounts. Configuring servers...`);

            // Configure accounts with server discovery
            const configuredAccounts = await AccountImportService.configureAccounts(
                parseResult.accounts,
                async (email) => {
                    const config = await getEmailConfig(email, sendLog);
                    return convertToEmailServerConfig(config);
                },
                (progress) => {
                    event.sender.send('import:progress', progress);
                }
            );

            // Add to store
            const newAccounts = await addAccounts(configuredAccounts);

            sendLog('success', `Successfully imported ${newAccounts.length} accounts`);

            return {
                success: true,
                addedCount: newAccounts.length,
                skippedCount: parseResult.skippedLines,
                totalCount: parseResult.totalLines,
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Import failed';
            sendLog('error', `Enhanced import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Register instant import handlers
 */
function registerInstantImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    // Instant import - adds accounts immediately and discovers DNS in background
    ipcMain.handle('accounts:import-from-file-instant', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Import Accounts (Instant)',
            buttonLabel: 'Import',
            properties: ['openFile'],
            filters: [
                { name: 'Text Files', extensions: ['txt', 'csv'] },
                { name: 'All Files', extensions: ['*'] }
            ],
        });

        if (canceled || filePaths.length === 0) {
            return { success: false, error: 'File selection canceled.' };
        }

        try {
            sendLog('info', 'Starting instant import...');
            const result = await InstantImportService.importFromFile(
                filePaths[0],
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Instantly imported ${result.addedCount} accounts. DNS discovery running in background.`);
            } else {
                sendLog('error', `Instant import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Instant import failed';
            sendLog('error', `Instant import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Register drag-and-drop import handlers
 */
function registerDragDropImportHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    // Import from specific file path (for drag-and-drop)
    ipcMain.handle('accounts:import-from-file-data', async (_event: IpcMainInvokeEvent, filePath: string) => {
        try {
            sendLog('info', 'Starting file import...');
            const result = await InstantImportService.importFromFile(
                filePath,
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Imported ${result.addedCount} accounts from dropped file. DNS discovery running in background.`);
            } else {
                sendLog('error', `File import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'File import failed';
            sendLog('error', `File import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });

    // Import from file content (for drag-and-drop)
    ipcMain.handle('accounts:import-from-file-content', async (_event: IpcMainInvokeEvent, content: string) => {
        try {
            sendLog('info', 'Starting content import...');
            const result = await InstantImportService.importFromContent(
                content,
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Imported ${result.addedCount} accounts from dropped content. DNS discovery running in background.`);
            } else {
                sendLog('error', `Content import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Content import failed';
            sendLog('error', `Content import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Main function to register all account handlers
 */
export const registerAccountHandlers = (ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void => {
    registerDiscoveryHandlers(ipcMain, sendLog);
    registerAccountCrudHandlers(ipcMain, sendLog);
    registerFileImportHandlers(ipcMain, mainWindow, sendLog);
    registerPreviewImportHandlers(ipcMain, mainWindow, sendLog);
    registerInstantImportHandlers(ipcMain, mainWindow, sendLog);
    registerDragDropImportHandlers(ipcMain, sendLog);
};