# Microsoft Accounts Support Guide

## Overview

The IMAP Viewer now supports importing Microsoft account formats with OAuth tokens. However, **Microsoft OAuth tokens cannot be used directly for IMAP connections**. This guide explains how to properly configure Microsoft accounts.

## Microsoft Account Format

The application recognizes Microsoft accounts in the following format:
```
email:password:oauth_token:uuid
email|password|oauth_token|uuid
```

Example:
```
<EMAIL>:temppass:M.C522_SN1.0.U.-[long_oauth_token]:8b4ba9dd-3ea5-4e5f-86f1-ddba2230dcf2
```

## Important: OAuth Tokens vs IMAP Access

### What Microsoft OAuth Tokens Are For:
- Microsoft Graph API access
- Office 365 services
- OneDrive, Teams, etc.
- **NOT for IMAP email access**

### For IMAP Email Access You Need:
1. **App Password** (recommended)
2. **OAuth2 IMAP tokens** (advanced)

## How to Set Up Microsoft Accounts

### Option 1: Use App Password (Recommended)

1. **Generate App Password:**
   - Go to Microsoft Account Security settings
   - Enable 2-factor authentication if not already enabled
   - Generate an App Password for "Mail" application
   - Use this App Password instead of your regular password

2. **Import Format:**
   ```
   <EMAIL>:your_app_password
   ```

### Option 2: OAuth2 for IMAP (Advanced)

This requires implementing proper OAuth2 flow for IMAP access, which is different from the general Microsoft OAuth tokens.

## Current Behavior

When you import Microsoft accounts with OAuth tokens:
1. ✅ **Parsing works** - accounts are recognized and imported
2. ❌ **IMAP connection fails** - OAuth tokens cannot be used for IMAP
3. 💡 **Clear error message** - explains what to do next

## Error Message

If you try to connect a Microsoft account with OAuth token, you'll see:
```
Microsoft OAuth accounts require App Password for IMAP access. 
Please generate an App Password in your Microsoft account settings 
and use it instead of the OAuth token.
```

## Next Steps

1. **For immediate use**: Generate App Passwords for your Microsoft accounts
2. **For future enhancement**: Implement proper OAuth2 IMAP flow
3. **Alternative**: Use the OAuth tokens for Microsoft Graph API integration

## Technical Details

- **Supported formats**: `:` and `|` separators
- **Token detection**: Tokens > 50 chars with special characters (*!$-.)
- **Account type**: Marked as `authType: 'microsoft'`
- **Storage**: OAuth tokens and UUIDs are preserved for future use
