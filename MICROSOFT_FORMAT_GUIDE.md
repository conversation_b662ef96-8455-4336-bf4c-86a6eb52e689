# Руководство по поддержке Microsoft формата аккаунтов

## Обзор

Программа теперь поддерживает импорт аккаунтов в Microsoft формате с OAuth токенами. Это позволяет импортировать аккаунты, которые используют современную аутентификацию Microsoft.

## Поддерживаемые форматы

### 1. Стандартный формат (как раньше)
```
<EMAIL>:password123
<EMAIL>;password123
<EMAIL>|password123
```

### 2. Microsoft формат с токенами
```
<EMAIL>:password:M.C543_BAY.0.U.-LongM<PERSON><PERSON>oftToken*WithSpecialChars!AndMore$:uuid
<EMAIL>|password|M.C522_SN1.0.U.-AnotherToken*Test!More$|uuid
```

### 3. Смешанный формат
Можно использовать оба формата в одном файле:
```
<EMAIL>:simplepassword
<EMAIL>:pass123:M.C543_BAY.0.U.-Token*WithSpecialChars!$:uuid-123
<EMAIL>:anotherpass
```

## Структура Microsoft формата

Microsoft формат состоит из 3-4 частей, разделенных `:` или `|`:

1. **Email адрес** - обычный email
2. **Пароль** - базовый пароль аккаунта
3. **Microsoft токен** - длинный токен OAuth (обычно начинается с `M.C` и содержит специальные символы)
4. **UUID** (опционально) - уникальный идентификатор

## Автоматическое распознавание

Программа автоматически определяет формат на основе:
- Количества частей в строке (3+ для Microsoft формата)
- Длины токена (более 50 символов)
- Наличия специальных символов в токене (`*`, `!`, `$`, `-`, `.`)
- Паттерна Microsoft токена (начинается с `M.C`)

## Как использовать

1. **Создайте файл** с аккаунтами в поддерживаемом формате
2. **Откройте программу** IMAPViewer
3. **Нажмите кнопку "Import"** в панели управления аккаунтами
4. **Выберите файл** с аккаунтами
5. **Программа автоматически** распознает и импортирует аккаунты

## Примеры файлов

### Файл только с Microsoft аккаунтами:
```
<EMAIL>:qVyu78sXj:M.C543_BAY.0.U.-Ct1POD4tD1o4qnMr1Mt9JThuoN0idXUeO8cP5QikqW5rKlzarEacJ5LKQP4HB*hBynR4dx*i5AfBA*:8b4ba9dd-3ea5-4e5f-86f1-ddba2230dcf2
<EMAIL>|fg3n8iXyB0j|M.C522_SN1.0.U.-CnHLaiNrJF0kkPjycrS4iErVqtldDVPJOBZwuiT19kaCcNPARX3UPEXM45kHM!g4uMNvH3LVDxsWSb5n5qVUQWp4ld8pwKS137cYxDU!|8b4ba9dd-3ea5-4e5f-86f1-ddba2230dcf2
```

### Смешанный файл:
```
<EMAIL>:password123
<EMAIL>:pass123:M.C543_BAY.0.U.-TestToken*WithSpecialChars!AndMore$:uuid-123
<EMAIL>:simplepass
<EMAIL>|pass456|M.C522_SN1.0.U.-AnotherToken*Test!More$|uuid-456
```

## Технические детали

- **Поддерживаемые разделители**: `:`, `|`, `;`, табуляция, запятая
- **Автоматическое определение** типа аутентификации
- **Сохранение токенов** в базе данных для будущего использования
- **Обратная совместимость** со старыми форматами
- **Логирование** процесса импорта для отладки

## Устранение неполадок

1. **Аккаунты не импортируются**: Проверьте формат файла и разделители
2. **Microsoft токены не распознаются**: Убедитесь, что токен длиннее 50 символов и содержит специальные символы
3. **Ошибки парсинга**: Проверьте логи в консоли разработчика

## Поддержка

Если у вас возникли проблемы с импортом Microsoft аккаунтов, проверьте:
- Формат файла соответствует примерам выше
- Токены содержат правильные символы
- Файл сохранен в кодировке UTF-8
